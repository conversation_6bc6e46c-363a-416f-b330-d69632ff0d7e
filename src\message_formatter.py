"""
Message Formatter Module for Financial News Translation <PERSON><PERSON>les message formatting, emojis, and styling for Telegram
"""
import json
import re
from datetime import datetime
from typing import Dict, List, Optional
from loguru import logger
from config import Config

class MessageFormatter:
    """Professional message formatter for Telegram posts"""
    
    def __init__(self, config: Config):
        self.config = config
        self.emojis = {}
        self.load_emojis()
    
    def load_emojis(self):
        """Load emoji mappings"""
        try:
            with open(self.config.FINANCIAL_TERMS_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.emojis = data.get('emojis', {})
            
            logger.info("Emojis loaded for message formatting")
            
        except Exception as e:
            logger.error(f"Error loading emojis: {e}")
            # Default emojis
            self.emojis = {
                'up': '📈', 'down': '📉', 'neutral': '➡️',
                'warning': '⚠️', 'fire': '🔥', 'rocket': '🚀',
                'money': '💰', 'chart': '📊', 'news': '📰',
                'analysis': '📊', 'translation': '🔄', 'pin': '📌',
                'time': '⏰', 'globe': '🌍'
            }
    
    def get_sentiment_emoji(self, sentiment: str) -> str:
        """Get emoji based on sentiment"""
        emoji_map = {
            'positive': self.emojis.get('up', '📈'),
            'negative': self.emojis.get('down', '📉'),
            'neutral': self.emojis.get('neutral', '➡️')
        }
        return emoji_map.get(sentiment, '📊')
    
    def format_title(self, original_text: str) -> str:
        """Format the title section"""
        # Truncate if too long
        if len(original_text) > 100:
            title = original_text[:97] + "..."
        else:
            title = original_text
        
        if self.config.USE_BOLD_TEXT:
            return f"*{title}*"
        return title
    
    def format_translation_section(self, translated_text: str) -> str:
        """Format the translation section"""
        translation_emoji = self.emojis.get('translation', '🔄')
        
        section = f"{translation_emoji} *ترجمه:*\n"
        section += f"{translated_text}"
        
        return section
    
    def format_analysis_section(self, analysis: str, sentiment: str) -> str:
        """Format the analysis section"""
        analysis_emoji = self.emojis.get('analysis', '📊')
        
        section = f"{analysis_emoji} *تحلیل:*\n"
        section += f"{analysis}"
        
        return section
    
    def format_footnote_section(self, key_info: Dict) -> str:
        """Format footnote with key terms and symbols"""
        if not key_info.get('financial_terms') and not key_info.get('market_symbols'):
            return ""
        
        footnote_parts = []
        
        # Add financial terms
        if key_info.get('financial_terms'):
            terms = key_info['financial_terms'][:3]  # Limit to 3 terms
            footnote_parts.append(f"*اصطلاحات کلیدی:* {', '.join(terms)}")
        
        # Add market symbols
        if key_info.get('market_symbols'):
            symbols = key_info['market_symbols'][:3]  # Limit to 3 symbols
            footnote_parts.append(f"*نمادها:* {', '.join(symbols)}")
        
        if footnote_parts:
            pin_emoji = self.emojis.get('pin', '📌')
            return f"\n{pin_emoji} *پ.ن:*\n" + "\n".join(footnote_parts)
        
        return ""
    
    def format_source_section(self, tweet_url: str, username: str) -> str:
        """Format source information"""
        globe_emoji = self.emojis.get('globe', '🌍')
        return f"\n{globe_emoji} *منبع:* [@{username}]({tweet_url})"
    
    def format_timestamp(self) -> str:
        """Format timestamp"""
        time_emoji = self.emojis.get('time', '⏰')
        now = datetime.now()
        persian_time = now.strftime("%H:%M")
        return f"\n{time_emoji} *زمان انتشار:* {persian_time}"
    
    def add_visual_separators(self, text: str) -> str:
        """Add visual separators between sections"""
        # Add subtle separators
        separator = "─" * 25
        sections = text.split('\n\n')
        return f"\n{separator}\n".join(sections)
    
    def format_numbers_persian(self, text: str) -> str:
        """Convert numbers to Persian in the final text"""
        if not text:
            return text
        
        english_to_persian = {
            '0': '۰', '1': '۱', '2': '۲', '3': '۳', '4': '۴',
            '5': '۵', '6': '۶', '7': '۷', '8': '۸', '9': '۹'
        }
        
        result = text
        for eng, per in english_to_persian.items():
            result = result.replace(eng, per)
        
        return result
    
    def clean_markdown(self, text: str) -> str:
        """Clean and validate Markdown formatting"""
        # Ensure proper bold formatting
        text = re.sub(r'\*([^*]+)\*', r'*\1*', text)
        
        # Remove excessive line breaks
        text = re.sub(r'\n{3,}', '\n\n', text)
        
        # Ensure proper spacing around emojis
        text = re.sub(r'([^\s])([📈📉📊🔄💰⚠️🔥🚀📌⏰🌍➡️📰])', r'\1 \2', text)
        text = re.sub(r'([📈📉📊🔄💰⚠️🔥🚀📌⏰🌍➡️📰])([^\s])', r'\1 \2', text)
        
        return text.strip()
    
    def create_professional_message(
        self,
        original_text: str,
        translated_text: str,
        analysis_data: Dict,
        tweet_data: Dict
    ) -> str:
        """Create a professionally formatted message"""
        try:
            message_parts = []
            
            # Title with original text
            title = self.format_title(original_text)
            message_parts.append(title)
            
            # Translation section
            translation_section = self.format_translation_section(translated_text)
            message_parts.append(translation_section)
            
            # Analysis section (if enabled)
            if self.config.INCLUDE_ANALYSIS and analysis_data.get('analysis'):
                analysis_section = self.format_analysis_section(
                    analysis_data['analysis'],
                    analysis_data.get('sentiment', 'neutral')
                )
                message_parts.append(analysis_section)
            
            # Footnote section
            footnote = self.format_footnote_section(analysis_data.get('key_info', {}))
            if footnote:
                message_parts.append(footnote)
            
            # Source section
            source_section = self.format_source_section(
                tweet_data.get('url', ''),
                tweet_data.get('username', '')
            )
            message_parts.append(source_section)
            
            # Timestamp
            if self.config.USE_EMOJIS:
                timestamp = self.format_timestamp()
                message_parts.append(timestamp)
            
            # Join all parts
            message = '\n\n'.join(message_parts)
            
            # Apply formatting options
            if self.config.USE_EMOJIS:
                # Emojis are already added in individual sections
                pass
            
            # Convert numbers to Persian if needed
            message = self.format_numbers_persian(message)
            
            # Clean markdown
            message = self.clean_markdown(message)
            
            # Ensure message is not too long (Telegram limit is 4096 characters)
            if len(message) > 4000:
                message = message[:3997] + "..."
            
            logger.info("Professional message formatted successfully")
            return message
            
        except Exception as e:
            logger.error(f"Error formatting message: {e}")
            # Fallback simple format
            return f"*خبر مالی جدید:*\n\n{translated_text}\n\n🌍 *منبع:* {tweet_data.get('username', 'Twitter')}"
    
    def create_simple_message(self, original_text: str, translated_text: str, tweet_data: Dict) -> str:
        """Create a simple formatted message (fallback)"""
        try:
            news_emoji = self.emojis.get('news', '📰')
            globe_emoji = self.emojis.get('globe', '🌍')
            
            message = f"{news_emoji} *خبر مالی:*\n\n"
            message += f"{translated_text}\n\n"
            message += f"{globe_emoji} *منبع:* [@{tweet_data.get('username', 'Twitter')}]({tweet_data.get('url', '')})"
            
            return self.clean_markdown(message)
            
        except Exception as e:
            logger.error(f"Error creating simple message: {e}")
            return f"خبر جدید: {translated_text}"
    
    def format_error_message(self, error_type: str, original_text: str = "") -> str:
        """Format error message for debugging"""
        warning_emoji = self.emojis.get('warning', '⚠️')
        
        error_messages = {
            'translation_failed': 'خطا در ترجمه متن',
            'analysis_failed': 'خطا در تحلیل خبر',
            'formatting_failed': 'خطا در فرمت‌بندی پیام',
            'api_error': 'خطا در ارتباط با API'
        }
        
        error_msg = error_messages.get(error_type, 'خطای نامشخص')
        
        message = f"{warning_emoji} *خطا:* {error_msg}\n\n"
        if original_text:
            message += f"*متن اصلی:* {original_text[:200]}..."
        
        return message
