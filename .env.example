# Twitter API Credentials
# Get these from https://developer.twitter.com/
TWITTER_BEARER_TOKEN=your_bearer_token_here
TWITTER_API_KEY=your_api_key_here
TWITTER_API_SECRET=your_api_secret_here
TWITTER_ACCESS_TOKEN=your_access_token_here
TWITTER_ACCESS_TOKEN_SECRET=your_access_token_secret_here

# Telegram Bot Configuration
# Get bot token from @BotFather on Telegram
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
# Channel ID (use @your_channel or -100xxxxxxxxx for private channels)
TELEGRAM_CHANNEL_ID=@your_channel_name

# OpenAI API Key (for enhanced translation and analysis)
# Get from https://platform.openai.com/
OPENAI_API_KEY=your_openai_api_key_here

# Google Translate API Key (backup translation service)
GOOGLE_TRANSLATE_API_KEY=your_google_translate_api_key_here

# Target Twitter Account to Monitor
TARGET_TWITTER_USERNAME=financialjuice

# Environment
ENVIRONMENT=development
