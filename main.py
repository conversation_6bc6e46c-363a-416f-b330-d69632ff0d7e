"""
Main module for Financial News Translation Bot
Integrates all components and manages the bot lifecycle
"""
import asyncio
import sys
import os
import signal
from datetime import datetime
from typing import Dict, Optional
from loguru import logger

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from config import get_config
from src.twitter_bot import TwitterBot
from src.translation_engine import TranslationEngine
from src.news_analyzer import NewsAnalyzer
from src.message_formatter import MessageFormatter
from src.telegram_bot import TelegramBot

class FinancialNewsBot:
    """Main bot class that orchestrates all components"""
    
    def __init__(self, config_name='default'):
        self.config = get_config(config_name)
        self.running = False
        self.setup_logging()
        
        # Initialize components
        self.twitter_bot = None
        self.translation_engine = None
        self.news_analyzer = None
        self.message_formatter = None
        self.telegram_bot = None
        
        # Statistics
        self.stats = {
            'tweets_processed': 0,
            'translations_completed': 0,
            'messages_sent': 0,
            'errors': 0,
            'start_time': None
        }
    
    def setup_logging(self):
        """Setup logging configuration"""
        try:
            # Remove default logger
            logger.remove()
            
            # Add console logger
            logger.add(
                sys.stdout,
                level=self.config.LOG_LEVEL,
                format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
            )
            
            # Add file logger
            os.makedirs(os.path.dirname(self.config.LOG_FILE), exist_ok=True)
            logger.add(
                self.config.LOG_FILE,
                level=self.config.LOG_LEVEL,
                format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
                rotation="1 day",
                retention="30 days"
            )
            
            logger.info("Logging setup completed")
            
        except Exception as e:
            print(f"Failed to setup logging: {e}")
            sys.exit(1)
    
    async def initialize_components(self):
        """Initialize all bot components"""
        try:
            logger.info("Initializing bot components...")
            
            # Initialize Twitter bot
            self.twitter_bot = TwitterBot(self.config)
            logger.info("✓ Twitter bot initialized")
            
            # Initialize translation engine
            self.translation_engine = TranslationEngine(self.config)
            logger.info("✓ Translation engine initialized")
            
            # Initialize news analyzer
            self.news_analyzer = NewsAnalyzer(self.config)
            logger.info("✓ News analyzer initialized")
            
            # Initialize message formatter
            self.message_formatter = MessageFormatter(self.config)
            logger.info("✓ Message formatter initialized")
            
            # Initialize Telegram bot
            self.telegram_bot = TelegramBot(self.config)
            
            # Test Telegram connection
            if await self.telegram_bot.test_connection():
                logger.info("✓ Telegram bot initialized and connected")
            else:
                raise Exception("Telegram bot connection failed")
            
            logger.info("All components initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize components: {e}")
            raise
    
    async def process_tweet(self, tweet_data: Dict) -> bool:
        """Process a single tweet through the entire pipeline"""
        try:
            tweet_id = tweet_data.get('id')
            original_text = tweet_data.get('text', '')
            
            logger.info(f"Processing tweet {tweet_id}: {original_text[:50]}...")
            
            # Update statistics
            self.stats['tweets_processed'] += 1
            
            # Step 1: Translate the tweet
            logger.debug("Starting translation...")
            translated_text = self.translation_engine.translate_text(original_text)
            
            if not translated_text or translated_text == original_text:
                logger.warning(f"Translation failed for tweet {tweet_id}")
                self.stats['errors'] += 1
                return False
            
            self.stats['translations_completed'] += 1
            logger.debug(f"Translation completed: {translated_text[:50]}...")
            
            # Step 2: Analyze the news
            logger.debug("Starting news analysis...")
            analysis_data = self.news_analyzer.analyze_news(original_text, translated_text)
            logger.debug(f"Analysis completed: {analysis_data.get('sentiment', 'unknown')} sentiment")
            
            # Step 3: Format the message
            logger.debug("Formatting message...")
            formatted_message = self.message_formatter.create_professional_message(
                original_text=original_text,
                translated_text=translated_text,
                analysis_data=analysis_data,
                tweet_data=tweet_data
            )
            
            # Validate message format
            if not self.telegram_bot.validate_message_format(formatted_message):
                logger.warning("Message format validation failed, using simple format")
                formatted_message = self.message_formatter.create_simple_message(
                    original_text, translated_text, tweet_data
                )
            
            # Step 4: Send to Telegram
            logger.debug("Sending to Telegram...")
            
            # Determine if this is important news (for pinning)
            is_important = (
                analysis_data.get('sentiment') in ['positive', 'negative'] and
                analysis_data.get('confidence', 0) > 0.7
            )
            
            result = await self.telegram_bot.send_formatted_news(
                formatted_message=formatted_message,
                pin_important=is_important
            )
            
            if result:
                self.stats['messages_sent'] += 1
                logger.info(f"✓ Tweet {tweet_id} processed and sent successfully")
                return True
            else:
                logger.error(f"Failed to send message for tweet {tweet_id}")
                self.stats['errors'] += 1
                return False
                
        except Exception as e:
            logger.error(f"Error processing tweet {tweet_data.get('id', 'unknown')}: {e}")
            self.stats['errors'] += 1
            
            # Send error notification in debug mode
            if self.config.DEBUG:
                error_msg = f"خطا در پردازش توییت: {str(e)}"
                await self.telegram_bot.send_error_notification(error_msg)
            
            return False
    
    async def monitor_tweets(self):
        """Main monitoring loop"""
        logger.info(f"Starting to monitor tweets from @{self.config.TARGET_TWITTER_USERNAME}")
        
        while self.running:
            try:
                # Get latest tweets
                new_tweets = self.twitter_bot.get_latest_tweets(
                    username=self.config.TARGET_TWITTER_USERNAME,
                    count=5
                )
                
                if new_tweets:
                    logger.info(f"Found {len(new_tweets)} new tweets")
                    
                    # Process each tweet
                    for tweet in new_tweets:
                        if not self.running:
                            break
                        
                        success = await self.process_tweet(tweet)
                        if success:
                            logger.info(f"Tweet {tweet['id']} processed successfully")
                        
                        # Small delay between processing tweets
                        await asyncio.sleep(2)
                
                # Wait before next check
                await asyncio.sleep(60)  # Check every minute
                
            except KeyboardInterrupt:
                logger.info("Received interrupt signal, stopping...")
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error
    
    async def start(self):
        """Start the bot"""
        try:
            logger.info("🚀 Starting Financial News Translation Bot")
            self.stats['start_time'] = datetime.now()
            
            # Initialize all components
            await self.initialize_components()
            
            # Send startup notification
            startup_msg = (
                f"🤖 *ربات مترجم اخبار مالی راه‌اندازی شد*\n\n"
                f"📊 *هدف:* @{self.config.TARGET_TWITTER_USERNAME}\n"
                f"⏰ *زمان شروع:* {datetime.now().strftime('%H:%M:%S')}\n"
                f"🔄 *وضعیت:* فعال"
            )
            
            await self.telegram_bot.send_message(startup_msg)
            
            # Set running flag
            self.running = True
            
            # Start monitoring
            await self.monitor_tweets()
            
        except Exception as e:
            logger.error(f"Failed to start bot: {e}")
            raise
    
    async def stop(self):
        """Stop the bot gracefully"""
        logger.info("Stopping bot...")
        self.running = False
        
        # Send shutdown notification
        if self.telegram_bot:
            uptime = datetime.now() - self.stats['start_time'] if self.stats['start_time'] else None
            
            shutdown_msg = (
                f"🛑 *ربات متوقف شد*\n\n"
                f"📊 *آمار عملکرد:*\n"
                f"• توییت‌های پردازش شده: {self.stats['tweets_processed']}\n"
                f"• ترجمه‌های انجام شده: {self.stats['translations_completed']}\n"
                f"• پیام‌های ارسالی: {self.stats['messages_sent']}\n"
                f"• خطاها: {self.stats['errors']}\n"
            )
            
            if uptime:
                hours, remainder = divmod(uptime.total_seconds(), 3600)
                minutes, _ = divmod(remainder, 60)
                shutdown_msg += f"• مدت فعالیت: {int(hours)}:{int(minutes):02d}\n"
            
            shutdown_msg += f"⏰ *زمان توقف:* {datetime.now().strftime('%H:%M:%S')}"
            
            await self.telegram_bot.send_message(shutdown_msg)
        
        logger.info("Bot stopped successfully")
    
    def print_stats(self):
        """Print current statistics"""
        print("\n" + "="*50)
        print("📊 BOT STATISTICS")
        print("="*50)
        print(f"Tweets processed: {self.stats['tweets_processed']}")
        print(f"Translations completed: {self.stats['translations_completed']}")
        print(f"Messages sent: {self.stats['messages_sent']}")
        print(f"Errors: {self.stats['errors']}")
        if self.stats['start_time']:
            uptime = datetime.now() - self.stats['start_time']
            print(f"Uptime: {uptime}")
        print("="*50)

async def main():
    """Main function"""
    # Get environment
    env = os.getenv('ENVIRONMENT', 'development')
    
    # Create bot instance
    bot = FinancialNewsBot(env)
    
    # Setup signal handlers for graceful shutdown
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}")
        asyncio.create_task(bot.stop())
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Start the bot
        await bot.start()
    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received")
    except Exception as e:
        logger.error(f"Bot crashed: {e}")
    finally:
        await bot.stop()
        bot.print_stats()

if __name__ == "__main__":
    # Run the bot
    asyncio.run(main())
