"""
Twitter Bot Module for Financial News Translation Bot
Handles Twitter API interactions and tweet monitoring
"""
import tweepy
import asyncio
import time
import sqlite3
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from loguru import logger
from config import Config

class TwitterBot:
    """Twitter Bot for monitoring and fetching tweets"""
    
    def __init__(self, config: Config):
        self.config = config
        self.api = None
        self.client = None
        self.last_tweet_id = None
        self.processed_tweets = set()
        self.setup_api()
        self.setup_database()
    
    def setup_api(self):
        """Initialize Twitter API connection"""
        try:
            # Twitter API v2 Client
            self.client = tweepy.Client(
                bearer_token=self.config.TWITTER_BEARER_TOKEN,
                consumer_key=self.config.TWITTER_API_KEY,
                consumer_secret=self.config.TWITTER_API_SECRET,
                access_token=self.config.TWITTER_ACCESS_TOKEN,
                access_token_secret=self.config.TWITTER_ACCESS_TOKEN_SECRET,
                wait_on_rate_limit=True
            )
            
            # Twitter API v1.1 for additional features
            auth = tweepy.OAuthHandler(
                self.config.TWITTER_API_KEY,
                self.config.TWITTER_API_SECRET
            )
            auth.set_access_token(
                self.config.TWITTER_ACCESS_TOKEN,
                self.config.TWITTER_ACCESS_TOKEN_SECRET
            )
            self.api = tweepy.API(auth, wait_on_rate_limit=True)
            
            # Test connection
            self.client.get_me()
            logger.info("Twitter API connection established successfully")
            
        except Exception as e:
            logger.error(f"Failed to setup Twitter API: {e}")
            raise
    
    def setup_database(self):
        """Setup SQLite database for tracking processed tweets"""
        try:
            conn = sqlite3.connect(self.config.DATABASE_PATH)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS processed_tweets (
                    tweet_id TEXT PRIMARY KEY,
                    username TEXT,
                    content TEXT,
                    created_at TIMESTAMP,
                    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            logger.info("Database setup completed")
            
        except Exception as e:
            logger.error(f"Failed to setup database: {e}")
            raise
    
    def is_tweet_processed(self, tweet_id: str) -> bool:
        """Check if tweet has already been processed"""
        try:
            conn = sqlite3.connect(self.config.DATABASE_PATH)
            cursor = conn.cursor()
            
            cursor.execute(
                "SELECT tweet_id FROM processed_tweets WHERE tweet_id = ?",
                (tweet_id,)
            )
            result = cursor.fetchone()
            conn.close()
            
            return result is not None
            
        except Exception as e:
            logger.error(f"Error checking processed tweet: {e}")
            return False
    
    def mark_tweet_processed(self, tweet_id: str, username: str, content: str, created_at: datetime):
        """Mark tweet as processed in database"""
        try:
            conn = sqlite3.connect(self.config.DATABASE_PATH)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO processed_tweets 
                (tweet_id, username, content, created_at)
                VALUES (?, ?, ?, ?)
            ''', (tweet_id, username, content, created_at))
            
            conn.commit()
            conn.close()
            logger.debug(f"Tweet {tweet_id} marked as processed")
            
        except Exception as e:
            logger.error(f"Error marking tweet as processed: {e}")
    
    def get_user_id(self, username: str) -> Optional[str]:
        """Get user ID from username"""
        try:
            user = self.client.get_user(username=username)
            if user.data:
                return user.data.id
            return None
            
        except Exception as e:
            logger.error(f"Error getting user ID for {username}: {e}")
            return None
    
    def get_latest_tweets(self, username: str, count: int = 10) -> List[Dict]:
        """Get latest tweets from specified user"""
        try:
            user_id = self.get_user_id(username)
            if not user_id:
                logger.error(f"Could not find user ID for {username}")
                return []
            
            # Get tweets with expanded fields
            tweets = self.client.get_users_tweets(
                id=user_id,
                max_results=count,
                tweet_fields=['created_at', 'public_metrics', 'context_annotations', 'entities'],
                exclude=['retweets', 'replies']
            )
            
            if not tweets.data:
                logger.info(f"No new tweets found for {username}")
                return []
            
            processed_tweets = []
            for tweet in tweets.data:
                # Skip if already processed
                if self.is_tweet_processed(tweet.id):
                    continue
                
                tweet_data = {
                    'id': tweet.id,
                    'text': tweet.text,
                    'created_at': tweet.created_at,
                    'username': username,
                    'url': f"https://twitter.com/{username}/status/{tweet.id}",
                    'metrics': tweet.public_metrics if hasattr(tweet, 'public_metrics') else {},
                    'entities': tweet.entities if hasattr(tweet, 'entities') else {}
                }
                
                processed_tweets.append(tweet_data)
                
                # Mark as processed
                self.mark_tweet_processed(
                    tweet.id, username, tweet.text, tweet.created_at
                )
            
            logger.info(f"Retrieved {len(processed_tweets)} new tweets from {username}")
            return processed_tweets
            
        except Exception as e:
            logger.error(f"Error getting latest tweets: {e}")
            return []
    
    def monitor_user_tweets(self, username: str, callback_func) -> None:
        """Monitor user tweets in real-time"""
        logger.info(f"Starting to monitor tweets from {username}")
        
        while True:
            try:
                new_tweets = self.get_latest_tweets(username, count=5)
                
                for tweet in new_tweets:
                    logger.info(f"New tweet detected: {tweet['id']}")
                    # Call the callback function to process the tweet
                    asyncio.create_task(callback_func(tweet))
                
                # Wait before next check (avoid rate limiting)
                time.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"Error in tweet monitoring: {e}")
                time.sleep(300)  # Wait 5 minutes on error
    
    def get_tweet_by_id(self, tweet_id: str) -> Optional[Dict]:
        """Get specific tweet by ID"""
        try:
            tweet = self.client.get_tweet(
                id=tweet_id,
                tweet_fields=['created_at', 'public_metrics', 'context_annotations', 'entities']
            )
            
            if tweet.data:
                return {
                    'id': tweet.data.id,
                    'text': tweet.data.text,
                    'created_at': tweet.data.created_at,
                    'url': f"https://twitter.com/i/status/{tweet_id}",
                    'metrics': tweet.data.public_metrics if hasattr(tweet.data, 'public_metrics') else {},
                    'entities': tweet.data.entities if hasattr(tweet.data, 'entities') else {}
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting tweet {tweet_id}: {e}")
            return None
    
    def search_tweets(self, query: str, count: int = 10) -> List[Dict]:
        """Search for tweets with specific query"""
        try:
            tweets = self.client.search_recent_tweets(
                query=query,
                max_results=count,
                tweet_fields=['created_at', 'public_metrics', 'context_annotations', 'entities']
            )
            
            if not tweets.data:
                return []
            
            processed_tweets = []
            for tweet in tweets.data:
                tweet_data = {
                    'id': tweet.id,
                    'text': tweet.text,
                    'created_at': tweet.created_at,
                    'url': f"https://twitter.com/i/status/{tweet.id}",
                    'metrics': tweet.public_metrics if hasattr(tweet, 'public_metrics') else {},
                    'entities': tweet.entities if hasattr(tweet, 'entities') else {}
                }
                processed_tweets.append(tweet_data)
            
            return processed_tweets
            
        except Exception as e:
            logger.error(f"Error searching tweets: {e}")
            return []
